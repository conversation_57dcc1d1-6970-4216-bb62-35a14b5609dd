from enum import Enum
from typing import Optional

from pydantic import BaseModel


class FusionTypes(Enum):
    ONTOLOGY = 'ontology'
    ENTITY = 'entity'


class KnowledgeFusionParams(BaseModel):
    task_id: int
    fusion_type: FusionTypes
    synonym_dictory_id: Optional[str] = None
    sim_threshold: Optional[float] = None


class TripleFusionParams(BaseModel):
    task_id: int
    fusion_type: Optional[FusionTypes] = None
