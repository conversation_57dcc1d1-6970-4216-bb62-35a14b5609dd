
from yunfu.common import LogUtils
from yunfu.db.graph.deprecated.core.models import Config
from yunfu.db.graph.deprecated.graph.graph_dbs import Neo4jGraphDb

from .base_graph_mapper import BaseGraphMapper

logger = LogUtils.get_logger(__name__)


class Neo4jGraphMapper(BaseGraphMapper):
    graph_db: Neo4jGraphDb

    def __init__(self, db_config: dict, enable_version: bool = True):
        self.graph_db = Neo4jGraphDb(
            Config(db=db_config, version={"enabled": enable_version})   # type: ignore
        )
