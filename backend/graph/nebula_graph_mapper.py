from yunfu.common import LogUtils
from yunfu.db.graph.deprecated.core.models import Config
from yunfu.db.graph.deprecated.graph.graph_dbs import NebulaGraphDb

from .base_graph_mapper import BaseGraphMapper

logger = LogUtils.get_logger(__name__)


class NebulaGraphMapper(BaseGraphMapper):

    graph_db: NebulaGraphDb
    # Nebula 暂时不支持下划线前缀，所以记录下带下划线的属性
    node_private_props = ["show_name", "type", "eid", "create_time", "update_time"]
    edge_private_props = ["rid", "create_time", "update_time"]

    def __init__(self, db_config: dict, enable_version: bool = True):
        self.graph_db = NebulaGraphDb(
            Config(db=db_config, version={"enabled": enable_version})
        )
