import os
from typing import List

import django
from django.conf import settings
from django.db import models
from yunfu.common import yfid

from backend.fusion.utils.db_con_util import close_old_database_connections

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "backend.settings")
django.setup()


class YfModel(models.Model):
    """基础model类

    """
    created_at = models.DateTimeField(verbose_name='创建时间', auto_now_add=True)
    updated_at = models.DateTimeField(verbose_name='修改时间', auto_now=True)

    class Meta:
        abstract = True
        ordering = ['id']


class Team(YfModel):
    """用户组

    """
    name = models.CharField(max_length=255, verbose_name='组名')
    conf = models.JSONField(verbose_name='组配置', default=dict)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, default=None)
    description = models.CharField(max_length=255, verbose_name='描述', blank=True, null=True, default='')
    is_activate = models.BooleanField(verbose_name='启用状态', default=True)

    class Meta:
        db_table = 'yunfu_due_common_team'


class Kg(YfModel):
    class KgRanges(models.IntegerChoices):
        PRIVATE = 1  # 私有
        GROUP_OPENNESS = 2  # 组内公开
        TOTAL_OPENNESS = 3  # 完全公开

    class TypeChoices(models.TextChoices):
        ONTOLOGY = 'ontology'  # 模版本体
        KG = 'kg'  # 图谱
        UPDATE = 'update'  # 更新用图谱

    name = models.CharField('名称', max_length=255)
    description = models.TextField(verbose_name='简介', null=True, blank=True)
    ontology_num = models.IntegerField(verbose_name='本体计数', default=0)
    property_num = models.IntegerField(verbose_name='属性计数', default=0)
    relation_num = models.IntegerField(verbose_name='关系计数', default=0)
    status = models.SmallIntegerField(verbose_name='状态', default=0)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name='操作用户')
    type = models.CharField('图谱类型', choices=TypeChoices.choices, default=TypeChoices.KG, max_length=50)
    team = models.ForeignKey(Team, verbose_name='用户组', on_delete=models.CASCADE, blank=True, null=True)
    range = models.IntegerField(verbose_name='可见范围', choices=KgRanges.choices, blank=True, default=1)
    parent = models.ForeignKey('self',
                               verbose_name='裁剪来源',
                               on_delete=models.SET_NULL,
                               blank=True,
                               null=True,
                               db_column='pid',
                               related_name='children')

    class Meta:
        verbose_name = 'kg'
        ordering = ['-updated_at']
        db_table = 'yunfu_due_kg_kg'


class TaskStatus(YfModel):
    class Status(models.IntegerChoices):
        # 与celery一致
        NO_STARTED = 0  # 未开始
        STARTED = 1  # 已开始
        SUCCESS = 2  # 执行完成
        FAILURE = -1  # 执行失败

    _task_status = models.IntegerField(verbose_name='任务状态', choices=Status.choices, default=Status.NO_STARTED)

    class Meta:
        abstract = True


class UpdateTask(TaskStatus):
    '''图谱更新任务'''
    class UpdateTypes(models.IntegerChoices):
        CREATE = 1  # 图谱构建
        INCREMENTAL_UPDATE = 2  # 增量更新
        FULL_UPDATE = 3  # 全量更新
        AUTO_UPDATE = 4  # 自动更新

    class TriggerTypes(models.IntegerChoices):
        MANUAL_UPDATE = 1  # 手动触发
        AUTO_UPDATE = 2  # 定时触发

    ontology = models.ForeignKey(Kg,
                                 on_delete=models.CASCADE,
                                 null=True,
                                 blank=True,
                                 verbose_name='本体',
                                 default=None,
                                 related_name='tasks')
    name = models.CharField(verbose_name='任务名', null=True, blank=True, max_length=100)
    user = models.ForeignKey(settings.AUTH_USER_MODEL,
                             on_delete=models.CASCADE,
                             verbose_name='创建人员',
                             blank=True,
                             null=True)
    status = models.SmallIntegerField(verbose_name='任务状态', default=0)
    created_at = models.DateTimeField(verbose_name='启动时间', auto_now=False, auto_now_add=True)
    time_consuming = models.CharField(verbose_name='耗时', null=True, blank=True, max_length=100)
    update_type = models.IntegerField(verbose_name='更新方式', choices=UpdateTypes.choices, null=True)
    trigger_type = models.IntegerField(verbose_name='触发方式', choices=TriggerTypes.choices, null=True)
    description = models.TextField(verbose_name='任务描述', null=True, blank=True)
    upload_file = models.CharField(verbose_name='上传的文件', null=True, blank=True, max_length=100)
    kg = models.ForeignKey(Kg,
                           on_delete=models.CASCADE,
                           verbose_name='对应主图图谱',
                           null=True,
                           blank=True,
                           default=None,
                           related_name='all_tasks')
    update_kg = models.ForeignKey(Kg,
                                  on_delete=models.CASCADE,
                                  verbose_name='对应所属图谱',
                                  null=True,
                                  blank=True,
                                  default=None,
                                  related_name='update_tasks')

    class Meta:
        db_table = 'yunfu_due_kg_updatetask'


class UpdateNode(YfModel):
    """待更新节点表"""
    class TypeChoices(models.IntegerChoices):
        ONTOLOGY = 1  # 本体
        ENTITY = 2  # 实体

    class FusionTypeChoices(models.IntegerChoices):
        FUSION = 1  # 待融合
        CREATE = 2  # 新增
        MERGE = 3  # 融合
        DROP = 4  # 删除

    class StatusChoices(models.IntegerChoices):
        FUSE_NODE = 1  # 融合节点
        FUSE_RELATION = 2  # 融合关系
        FUSE_PROPERTY = 3  # 融合属性
        SUCCESS = 4  # 融合完成

    task = models.ForeignKey(UpdateTask, on_delete=models.CASCADE, verbose_name='更新任务', related_name='update_nodes')
    name = models.CharField(verbose_name='节点名', max_length=100)
    eid = models.CharField(verbose_name='节点图数据库eid', max_length=100)
    main_eid = models.CharField(verbose_name='新增/融合后主图数据库eid', max_length=100, default='')
    type = models.IntegerField(verbose_name='节点类型', choices=TypeChoices.choices, default=TypeChoices.ONTOLOGY)
    label = models.CharField(verbose_name='节点标签', max_length=100, default=None)
    fusion_type = models.IntegerField(verbose_name='融合类型',
                                      choices=FusionTypeChoices.choices,
                                      default=FusionTypeChoices.FUSION)
    status = models.IntegerField(verbose_name='融合状态', choices=StatusChoices.choices, default=StatusChoices.FUSE_NODE)

    class Meta:
        db_table = 'yunfu_due_kg_updatenode'
        indexes = [
            models.Index(fields=['task', 'eid']),
            models.Index(fields=['task', 'main_eid']),
        ]


class BaseStatusModel(YfModel):
    """带状态的模型基类"""
    class StatusChoices(models.IntegerChoices):
        UNSELECTED = 1  # 待处理
        MERGE = 2  # 融合
        ADD = 3  # 新增
        DROP = 4  # 放弃

    status = models.IntegerField(verbose_name='状态', choices=StatusChoices.choices, default=StatusChoices.UNSELECTED)

    class Meta:
        abstract = True


class UpdateNodeCandidate(BaseStatusModel):
    """节点候选"""

    node = models.ForeignKey(UpdateNode, on_delete=models.CASCADE, verbose_name='节点候选', related_name='candidates')
    name = models.CharField(verbose_name='节点名', max_length=100)
    score = models.FloatField(verbose_name='得分')
    eid = models.CharField(verbose_name='节点图数据库eid', max_length=100)
    label = models.CharField(verbose_name='节点标签', max_length=100, default=None)

    class Meta:
        db_table = 'yunfu_due_kg_updatenodecandidate'


class UpdatePropertyCandidate(BaseStatusModel):
    """节点冲突属性"""

    node = models.ForeignKey(UpdateNode, on_delete=models.CASCADE, verbose_name='关联节点', related_name='properties')
    name = models.CharField(verbose_name='属性名', max_length=100)
    candidate = models.CharField(verbose_name='候选属性名', max_length=100)
    value = models.CharField(verbose_name='属性值', max_length=100)
    score = models.FloatField(verbose_name='得分')

    class Meta:
        db_table = 'yunfu_due_kg_updatepropertycandidate'


class UpdateRelationCandidate(BaseStatusModel):
    """节点冲突关系"""

    node = models.ForeignKey(UpdateNode, on_delete=models.CASCADE, verbose_name='关联节点', related_name='relations')
    name = models.CharField(verbose_name='关系名', max_length=100)
    candidate = models.CharField(verbose_name='候选关系名', max_length=100)
    score = models.FloatField(verbose_name='得分')
    relation = models.JSONField(verbose_name='关系数据', null=True, blank=True)  # TODO 关联节点已被融合问题

    class Meta:
        db_table = 'yunfu_due_kg_updaterelationcandidate'


class ResourceChangeLog(YfModel):
    resource_name = models.CharField(verbose_name='资源名', max_length=100)
    resource_id = models.CharField(verbose_name='资源id', max_length=100)
    resource_type = models.CharField(verbose_name='资源类别', max_length=100)
    yfid = models.CharField(verbose_name='唯一标识', max_length=30, default='', blank=True)
    operation = models.CharField(verbose_name='操作', max_length=100)
    value = models.TextField(verbose_name='操作值', null=True, blank=True)
    sources = models.JSONField(verbose_name='操作来源资源', max_length=100, null=True, blank=True)
    detail = models.JSONField(verbose_name='操作信息详情', null=True, blank=True, default=[])

    class Meta:
        ordering = ['-updated_at']
        verbose_name = '数据溯源记录表'
        db_table = 'yunfu_due_data_resourcechangelog'

    @classmethod
    def create_yfid(cls, resource_name: str, resource_id: str, operation: str, value: str) -> str:
        yfid_: str = yfid(f"{resource_name}__{resource_id}__{operation}__{value}")
        return yfid_


class TaskLogger(YfModel):
    """时间统计"""
    class TypeChoices(models.TextChoices):
        EXTRACT = 'extractor'  # extractor服务
        SAVER = 'saver'  # saver服务
        ONTOLOGY_ADD = 'fusion_ontology_auto_add'  # fusion服务本体自动新增
        ONTOLOGY_FUSION = 'fusion_ontology_auto_fusion'  # fusion服务本体自动融合
        ENTITY_ADD = 'fusion_entity_auto_add'  # fusion服务实体自动新增
        ENTITY_FUSION = 'fusion_entity_auto_fusion'  # fusion服务实体自动融合

    service_type = models.CharField(verbose_name='服务类型', choices=TypeChoices.choices, max_length=250)
    started_at = models.DateTimeField(verbose_name='请求的时间')
    service_started_at = models.DateTimeField(verbose_name='任务开始的时间')
    ended_at = models.DateTimeField(verbose_name='任务结束的时间')
    update_task = models.ForeignKey(UpdateTask,
                                    on_delete=models.CASCADE,
                                    verbose_name='任务',
                                    null=True,
                                    blank=True,
                                    default=None)

    class Meta:
        db_table = 'yunfu_due_kg_tasklogger'


class ExtractTask(YfModel):
    """抽取任务表"""
    class ExtractTaskMethod(models.TextChoices):
        """实体关系属性枚举"""
        RDF_XML = 'rdf-xml'
        RDF_TTL = 'rdf-ttl'
        TRIPLE = 'triple'  # 抽取
        XLSX = 'xlsx'  # 抽取
        DOCX = 'docx'  # 抽取
        HTML = 'html'  # html
        PDF = 'pdf'  # 模型
        SECOND_DECIMATION = 'second_decimation'  # 二次抽取
        PATENT_PDF = 'patent_pdf'  # 专利pdf
        TEXT_BOOK = 'text_book'  # 教材 pdf
        CNKI_JOURNAL = 'CNKI_journal'  # 期刊摘要 html
        HEMATOLOLINE_JOURNAL = 'Hematoline_journal'  # 期刊摘要 html
        WFDATA_JOURNAL = 'WFDATA_journal'  # 期刊摘要 html
        MEDLIVE_GUIDE = 'Medlive_guide'  # 医脉通医学指南 pdf
        NHC_GUIDE = 'NHC_guide'  # 卫健委医学指南 PDF
        HEMATIOLINE_GUIDE = 'Hematioline_guide'  # 医学指南 HTML

    name = models.CharField(verbose_name='任务名', max_length=100, default='', blank=True)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, verbose_name='创建人员', on_delete=models.CASCADE)
    status = models.SmallIntegerField(verbose_name='状态', default=0, blank=True)  # 枚举
    remark = models.CharField(verbose_name='备注', max_length=100, default='', blank=True, null=True)
    method = models.CharField(verbose_name='抽取方式', max_length=100, null=True, choices=ExtractTaskMethod.choices)
    upload_file = models.CharField(verbose_name='文件', max_length=100, default='', blank=True)

    class Meta:
        verbose_name = '抽取任务'
        db_table = 'yunfu_due_data_extracttask'


class TripleData(YfModel):
    """三元组数据表"""
    class TypeChoices(models.IntegerChoices):
        ENTITY = 0  # 实体
        PROPERTY = 1  # 属性
        RELATION = 2  # 关系

    class FusionTypeChoices(models.IntegerChoices):
        CREATE = 1  # 新增
        MERGE = 2  # 融合
        DROP = 3  # 删除

    class SourceType(models.IntegerChoices):
        '''来源类型
        :param EXTRACT: 抽取
        :param COMPLETE: 补全
        '''
        EXTRACT = 0
        COMPLETE = 1

    class StatusType(models.IntegerChoices):
        '''状态类型
        :param USER_ADD: 用户新增
        :param USER_FUSE: 用户融合
        :param USER_DELETE: 用户删除
        :param AUTO_ADD: 自动新增
        :param AUTO_FUSE: 自动融合
        :param AUTO_DELETE: 自动删除
        '''

        USER_ADD = 0
        USER_FUSE = 1
        USER_DELETE = 2
        AUTO_ADD = 3
        AUTO_FUSE = 4
        AUTO_DELETE = 5

    subject = models.CharField(verbose_name='主体', max_length=512, default='', blank=True)
    subject_type = models.CharField(verbose_name='主体类型', max_length=100, default='', blank=True)
    subject_eid = models.CharField(verbose_name='主体eid', max_length=100, default='', blank=True)
    object = models.CharField(verbose_name='客体', max_length=512, default='', blank=True)
    object_type = models.CharField(verbose_name='客体类型', max_length=100, default='', blank=True)
    object_eid = models.CharField(verbose_name='客体eid', max_length=100, blank=True, null=True)
    predicate = models.CharField(verbose_name='谓词', max_length=100, default='', blank=True)
    type = models.IntegerField(verbose_name='三元组类型', choices=TypeChoices.choices, default=None, blank=True)
    file_line = models.IntegerField(verbose_name='行数', default=0, blank=True)
    count = models.IntegerField(verbose_name='次数', default=0, blank=True)
    yfid = models.CharField(verbose_name='唯一标识', max_length=30, unique=True, blank=True)
    task = models.ForeignKey(UpdateTask, on_delete=models.CASCADE, null=True, blank=True, verbose_name='更新任务')
    extract_task = models.ForeignKey(ExtractTask, on_delete=models.CASCADE, null=True, blank=True, verbose_name='抽取任务')
    file_id = models.CharField(verbose_name='文件id', max_length=100, default='', blank=True)
    file_name = models.CharField(verbose_name='文件名', max_length=100, default='', blank=True)
    second_decimation = models.BooleanField(verbose_name='二次抽取', null=False, default=False)
    extraction_rules = models.CharField(verbose_name='抽取函数', max_length=100, default='', blank=True)
    fusion_type = models.IntegerField(verbose_name='融合操作类型', choices=FusionTypeChoices.choices, blank=True, null=True)
    fusion_subject = models.CharField(verbose_name='融合后主体', max_length=512, blank=True, null=True)
    fusion_subject_eid = models.CharField(verbose_name='融合后主体eid', max_length=100, blank=True, null=True)
    fusion_object = models.CharField(verbose_name='融合后客体', max_length=512, blank=True, null=True)
    fusion_object_eid = models.CharField(verbose_name='融合后客体eid', max_length=100, blank=True, null=True)
    fusion_predicate = models.CharField(verbose_name='融合后谓词', max_length=100, blank=True, null=True)
    status = models.IntegerField(verbose_name='状态', choices=StatusType.choices, blank=True, null=True)
    source = models.IntegerField(verbose_name='来源', choices=SourceType.choices, blank=True, null=True)
    oper_user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name='操作用户')

    class Meta:
        verbose_name = '三元组数据'
        db_table = 'yunfu_due_data_tripledata'
        indexes = [
            models.Index(fields=['yfid']),
        ]

    def __str__(self):
        return f'("subject": {self.subject}, "predicate": {self.predicate}, "object": {self.object})'

    @classmethod
    def update_fusion_node(cls, yfid, fusion_subject, fusion_subject_eid, fusion_predicate, fusion_object,
                           fusion_object_eid, source) -> None:
        """更新三元组数据"""
        triple_data = cls.objects.get(yfid=yfid)
        triple_data.fusion_subject = fusion_subject
        triple_data.fusion_subject_eid = fusion_subject_eid
        triple_data.fusion_predicate = fusion_predicate
        triple_data.fusion_object = fusion_object
        triple_data.fusion_object_eid = fusion_object_eid
        triple_data.source = source
        triple_data.save()

    @classmethod
    @close_old_database_connections
    def _bulk_update_triple_data(cls, triples: List) -> None:
        TripleData.objects.bulk_update(triples, [
            'subject_eid', 'object_eid', 'fusion_subject', 'fusion_subject_eid', 'fusion_object', 'fusion_object_eid',
            'fusion_predicate', 'status'
        ], 200)


class EntityAlign(YfModel):
    """实体对齐表"""
    class FusionTypeChoices(models.IntegerChoices):
        CREATE = 1  # 新增
        MERGE = 2  # 融合

    class TypeChoices(models.IntegerChoices):
        ONTOLOGY = 1  # 本体
        ENTITY = 2  # 实体

    kg = models.ForeignKey(Kg, on_delete=models.CASCADE, verbose_name='溯源图谱')
    name = models.CharField(verbose_name='实体名称', max_length=100, default='', blank=True)
    eid = models.CharField(verbose_name='实体eid', max_length=100, default='', blank=True)
    type = models.CharField(verbose_name='实体类别', max_length=100, default='', blank=True)
    score = models.FloatField(verbose_name='分数', default=0)
    task = models.ForeignKey(UpdateTask, on_delete=models.CASCADE, verbose_name='更新任务', related_name='entity_aligns')
    status = models.IntegerField(verbose_name='融合状态', choices=FusionTypeChoices.choices)
    node_type = models.IntegerField(verbose_name='节点类型', choices=TypeChoices.choices)
    default_select = models.CharField(verbose_name='默认选中', max_length=100, default='', blank=True)

    class Meta:
        verbose_name = '实体对齐'
        db_table = 'yunfu_due_kg_entityalign'


class Source(YfModel):
    """来源"""
    kg = models.ForeignKey(Kg, on_delete=models.CASCADE, verbose_name='溯源图谱')
    name = models.CharField(verbose_name='来源名称', max_length=100, default='', blank=True)
    eid = models.CharField(verbose_name='实体eid', max_length=100, default='', blank=True)
    entity = models.ForeignKey(EntityAlign, on_delete=models.CASCADE, verbose_name='实体对齐')

    class Meta:
        verbose_name = '实体对齐溯源表'
        db_table = 'yunfu_due_kg_source'


class Candidate(YfModel):
    """候选实体"""
    name = models.CharField(verbose_name='实体名称', max_length=100, default='', blank=True)
    eid = models.CharField(verbose_name='实体eid', max_length=100, default='', blank=True)
    score = models.FloatField(verbose_name='分数', default=0)
    entity = models.ForeignKey(EntityAlign, related_name='candidates', on_delete=models.CASCADE, verbose_name='实体对齐')

    class Meta:
        verbose_name = '实体对齐候选表'
        db_table = 'yunfu_due_kg_candidate'


class FusionTriple(YfModel):
    """三元组融合表"""
    class FusionTypeChoices(models.IntegerChoices):
        CREATE = 1  # 新增
        MERGE = 2  # 融合

    class TripleType(models.IntegerChoices):
        ATTRIBUTE = 1
        RELATION = 2

    class TypeChoices(models.IntegerChoices):
        ONTOLOGY = 1  # 本体
        ENTITY = 2  # 实体

    triple = models.TextField(verbose_name='待融合三元组', default='', blank=True)
    status = models.IntegerField(verbose_name='融合状态', choices=FusionTypeChoices.choices)
    task = models.ForeignKey(UpdateTask, on_delete=models.CASCADE, verbose_name='更新任务', related_name='fusion_triples')
    type = models.IntegerField(verbose_name='类别', choices=TripleType.choices)
    node_type = models.IntegerField(verbose_name='节点类型', choices=TypeChoices.choices)
    default_select = models.CharField(verbose_name='默认选中', max_length=100, default='', blank=True)

    class Meta:
        verbose_name = '三元组对齐候选表'
        db_table = 'yunfu_due_kg_fusiontriple'


class TripleSource(YfModel):
    """三元组来源表"""
    # TODO: 命名修改下，有的是动宾短语，有的是名词加动词，
    triple = models.TextField(verbose_name='三元组', default='', blank=True)
    fusion_triple = models.ForeignKey(FusionTriple,
                                      related_name='sources',
                                      on_delete=models.CASCADE,
                                      verbose_name='三元组对齐')
    kg = models.ForeignKey(Kg, on_delete=models.CASCADE, verbose_name='溯源图谱')

    class Meta:
        verbose_name = '三元组来源表'
        db_table = 'yunfu_due_kg_triplesource'
