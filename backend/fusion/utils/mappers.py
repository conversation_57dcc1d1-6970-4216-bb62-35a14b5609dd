import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from elasticsearch_dsl import connections
from yfflow import <PERSON><PERSON><PERSON><PERSON><PERSON>

from backend.documents import Linker
from yunfu.common import ConfigUtils, SingletonMeta, StatClient, timing
from yunfu.db.graph.deprecated.graph import Neo4jGraph
from yunfu.db.graph.deprecated.models import Edge, Node
from yunfu.db.graph.deprecated.neo4j_client import GraphDatabase, Neo4jClient

from .utils import TimeUtils

logger = YfLogger(__name__)
conf = ConfigUtils.load('conf/config.yaml')
default_version = conf['default_name']
stat_client = StatClient(**conf['stat_conf'])


class FuseStatus(Enum):
    UNLINKED = -1
    CANDIDATE = 0
    LINK = 1
    CHILD = 2


class GraphMapper(Neo4jClient):
    __metaclass__ = SingletonMeta

    def __init__(self, config: Dict) -> None:
        uri = f"{config['db']['schema']}://{config['db']['host']}:{config['db']['port']}"
        auth = (config['db']['username'], config['db']['password'])
        conf = {
            'host': config['db']['host'],
            'port': config['db']['port'],
            'username': config['db']['username'],
            'password': config['db']['password']
        }
        self.graph = GraphDatabase.driver(uri, auth=auth)
        self.min_count = config.get('min_count', 5)
        self.neo4j_graph = Neo4jGraph(conf, space='default', use_version=True, stat_client=stat_client)

    @timing(stat_client, 'yfproduct-yfkm-fusion', tags={'step': 'graph_get_category_names'})
    def get_category_names(self, node: Node, label: str) -> Optional[str]:
        ontology_label = label.replace('_category', '') + '_category'
        match = self.neo4j_graph.edges.match(nodes=[node, None], r_type='属于').where(f'b:{ontology_label}')
        match_result = match.first()
        if match_result:
            return match_result.dest_node.properties['name']  # type: ignore
        return None

    @timing(stat_client, 'yfproduct-yfkm-fusion', tags={'step': 'graph_get_node_by_eid_with_labels'})
    def get_node_by_eid_with_labels(self, labels: List[str], eid: str) -> Node:
        nodes = self.get_nodes(0, 1, *labels, **{'_eid': eid})
        if len(nodes) == 0:
            raise ValueError('实体不存在')
        return nodes[0]

    @timing(stat_client, 'yfproduct-yfkm-fusion', tags={'step': 'graph_get_nodes_with_eids'})
    def get_nodes_with_eids(self, labels: List[str], eids: List[str]) -> List[Node]:
        nodes: List[Node] = self.neo4j_graph.nodes.match().where(f"_:{':'.join(labels)}").where(
            f"_._eid IN {eids}").all()
        return nodes

    @timing(stat_client, 'yfproduct-yfkm-fusion', tags={'step': 'graph_get_edges_with_eids'})
    def get_edges_with_eids(self, labels: List[str], eids: List[str]) -> List[Edge]:
        query = f'''
            MATCH p=(n:{':'.join(labels)})-[r]->(m) WHERE n._eid in {eids} return p
        '''
        results = self.run(query)
        return [self._parse_path(result[0]) for result in results]

    @timing(stat_client, 'yfproduct-yfkm-fusion', tags={'step': 'graph_get_category_edges_with_eids'})
    def get_category_edges_with_eids(self, labels: List[str], eids: List[str]) -> List[Edge]:
        query = f'''
            MATCH p=(n:{':'.join(labels)})-[r:属于]->(m) WHERE n._eid in {eids} return p
        '''
        results = self.run(query)
        return [self._parse_path(result[0]) for result in results]

    @timing(stat_client, 'yfproduct-yfkm-fusion', tags={'step': 'graph_get_all_nodes_with_labels'})
    def get_all_nodes_with_labels(self, labels: List[str], skip: int, limit: int) -> List[Node]:
        logger.info(f'''where query is {f"_:{':'.join(labels)}"}''')
        nodes: List[Edge] = self.neo4j_graph.nodes.match().where(f"_:{':'.join(labels)}").skip(skip).limit(limit).all()
        return nodes

    def get_nodes_with_labels(self, labels: list, exclude_labels: list, limit: int, skip: int):
        match = self.neo4j_graph.nodes.match(*labels)
        if exclude_labels:
            match = match.where(f"NOT _:{':'.join(exclude_labels)}")
        return match.limit(limit).skip(skip).all()

    @timing(stat_client, 'yfproduct-yfkm-fusion', tags={'step': 'graph_get_edges_with_eid'})
    def get_edges_with_eid(self,
                           node_labels1: List[str],
                           eid: str,
                           node_labels2: Optional[List[str]] = None,
                           mode: str = 'create') -> Any:  # noqa
        logger.info(f'node_labels1 : {node_labels1}')
        logger.info(f'node_labels2 : {node_labels2}')  # None
        match = self.neo4j_graph.edges.match().where(f"a._eid='{eid}'")
        logger.info(f'eid = {eid}')
        match = match.where(f"a:{':'.join(node_labels1)}")
        if node_labels2:
            match = match.where(f"b:{':'.join(node_labels2)}")
        else:
            match = match.where(f"b:{':'.join(node_labels1)}")
        logger.info(f'match : {match}')
        out_edges = match.all()
        if mode == 'create':
            return out_edges
        match = self.neo4j_graph.edges.match().where(f"b._eid='{eid}'")
        match = match.where(f"b:{':'.join(node_labels1)}")
        if node_labels2:
            match = match.where(f"a:{':'.join(node_labels2)}")
        else:
            match = match.where(f"a:{':'.join(node_labels1)}")
        in_edges = match.all()
        return out_edges + in_edges

    @timing(stat_client, 'yfproduct-yfkm-fusion', tags={'step': 'graph_get_edges_without_eids_and_labels'})
    def get_edges_without_eids_and_labels(self, eid1: str, eid2: str, labels: List[str]) -> Any:  # noqa
        """获取不带eid和标签的数据

        :param eid1: eid1
        :type eid1: str
        :param eid2: eid1
        :type eid2: str
        :param labels: 标签
        :type labels: str
        :return: 查询数据
        :rtype: list
        """
        logger.info(f" a._eid='{eid1}' b._eid='{eid2} labels is {labels} a:{':'.join(labels)} b:{':'.join(labels)}")
        match = self.neo4j_graph.edges.match().where(f"a._eid='{eid1}'").where(f"b._eid='{eid2}'")
        match = match.where(f"a:{':'.join(labels)}").where(f"b:{':'.join(labels)}")
        return match.all()

    def create_node(self, node: Node) -> Node:
        now_time = TimeUtils.now_str()
        node.properties['_create_time'] = now_time
        node.properties['_update_time'] = now_time
        return self.neo4j_graph.create(node)

    def create_edge(self, edge: Edge) -> Edge:
        now_time = TimeUtils.now_str()
        properties = edge.properties
        properties["_create_time"] = now_time
        properties["_update_time"] = now_time
        return self.neo4j_graph.create(edge)

    def get_nodes(self, skip: int = 0, limit: int = 20, *labels, **properties) -> Any:  # noqa
        return self.neo4j_graph.nodes.match(*labels, **properties).limit(limit).skip(skip).all()

    def partial_update_node(self, node: Node) -> Node:
        now_time = TimeUtils.now_str()
        neo4j_node = self._get_node(node.id)
        create_time = neo4j_node.get("_create_time")
        node.properties["_create_time"] = create_time if create_time else neo4j_node["create_time"]
        node.properties["_update_time"] = now_time
        return self.neo4j_graph.update(node)


class SearchMapper(metaclass=SingletonMeta):
    def __init__(self, configs: Dict) -> None:
        logger.info(f'es init: configs{configs}')
        self.configs = configs
        conn = connections.Connections()
        self.client = conn.create_connection(hosts=[configs['host']], alias='kg', timeout=30)
        self.indices_client = self.client.indices

    def create_entity(self, index_name: str, name: str, type_: str, project: str, id_: str) -> None:
        table_name = Linker
        now = datetime.datetime.now()
        entity = table_name(_id=id_,
                            name=name,
                            source_type=3,
                            type=type_,
                            status=1,
                            created_at=now,
                            updated_at=now,
                            version=default_version)
        entity.save(index=index_name, refresh=True, using=self.client)
        logger.info(f'[Create Entity]{entity}')
