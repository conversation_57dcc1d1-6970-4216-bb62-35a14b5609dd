from typing import Dict, List, Optional, Tuple

from backend.fusion.schema.attribute import Attribute
from backend.fusion.schema.entity import Entity
from backend.fusion.schema.entity_triple import EntityTriples
from backend.fusion.schema.relation import Relation
from backend.fusion.utils.es_utils import ESUtils
from backend.fusion.utils.neo4j_utils import Neo4jUtils


class CandidateGenerator:
    def __init__(self, conf: Dict) -> None:
        self.es_utils = ESUtils(conf)
        self.neo4j_utils = Neo4jUtils(conf)
        self.delete_attributes = conf['candidate_generator']['delete_attributes']

    def generate(
            self, to_fusion_entity: str, base_kg: str,
            fusion_type: str, synonym_dictory_id: Optional[str] = None
    ) -> Tuple[List[EntityTriples], List[EntityTriples]]:
        # candidates: List[EntityTriples] = []
        synonym_entities = []
        if synonym_dictory_id:
            synonym = self.es_utils.get_synonyms_by_name(to_fusion_entity, synonym_dictory_id)
            if synonym:
                synonym_entities = synonym.synonyms
                synonym_entities.remove(to_fusion_entity)
        candidates = self.entity2candidates(to_fusion_entity, base_kg, fusion_type)
        synonym_candidates = []
        for entity in synonym_entities:
            synonym_candidates.extend(self.entity2candidates(entity, base_kg, fusion_type))
        return candidates, synonym_candidates

    def entity2candidates(self, entity, base_kg, fusion_type):
        candidates = []
        candidate_entities = self.es_utils.get_entities_by_name(entity, base_kg, fusion_type)
        for candidate_entity in candidate_entities:
            type_ = candidate_entity.type if candidate_entity.type else '其他'
            candidate_entity_dict = candidate_entity.to_dict()
            eid = candidate_entity.eid if 'eid' in candidate_entity_dict else candidate_entity.meta.id
            entity = Entity(
                eid=eid, name=candidate_entity.name, type=type_)
            candidate = self.generate_candidate(entity, base_kg, fusion_type, False)
            candidates.append(candidate)
        return candidates

    def generate_candidate(
            self, entity: Entity, base_kg: str, fusion_type: str, get_triple: bool = True,
            score: float = 0.0) -> EntityTriples:
        if get_triple:
            relations = self._get_relations_from_neo4j(entity.eid, base_kg, fusion_type)
            attributes = self._get_attributes_from_neo4j(entity.eid, base_kg)
        else:
            relations = []
            attributes = []
        candidate = EntityTriples(head=entity, relations=relations, attributes=attributes, score=score)
        return candidate

    def _get_attributes_from_neo4j(self, eid: str, base_kg: str) -> List[Attribute]:
        attributes: List[Attribute] = []
        node = self.neo4j_utils.get_node_by_eid(eid, base_kg)
        for key, value in node.properties.items():
            if key in self.delete_attributes:
                continue
            attribute = Attribute(attribute_name=key, attribute_value=value)
            attributes.append(attribute)
        return attributes

    def _get_relations_from_neo4j(self, eid: str, base_kg: str, fusion_type: str) -> List[Relation]:
        relations: List[Relation] = []
        paths = self.neo4j_utils.get_edge_by_head_eid(eid, base_kg)
        for path in paths:
            dict_path = path.to_json()
            relation = dict_path['properties']['name']
            if fusion_type == 'ontology' and 'concept' not in dict_path['dest_node'].labels:
                continue
            tail = Entity(eid=dict_path['dest_node'].properties['_eid'],
                          name=dict_path['dest_node'].properties['name'],
                          type=dict_path['dest_node'].properties.get('_type', '其他'))
            relation = Relation(relation=relation, tail=tail, type='base')
            relations.append(relation)
        return relations
