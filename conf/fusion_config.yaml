es:
  add: !env ${ES_HOST|http://yftool-db-elasticsearch:9200}
  entity_index: kg_nodes

candidate_generator:
  candidate_count: 5
  delete_attributes:
    [
      "create_time",
      "update_time",
      "_show_name",
      "_eid",
      "name",
      "_create_time",
      "_update_time",
      "_type",
    ]

neo4j:
  schema: "bolt"
  host: !env ${GRAPH_HOST|yftool-db-neo4j}
  port: !env ${GRAPH_PORT|7687}
  username: !env ${GRAPH_USERNAME|neo4j}
  password: !env ${GRAPH_PASSWORD|yunfu2017}

fast_sim:
  idf_file: /opt/yunfu/yfproduct/yfkm/services/kg/fusion/data/idf.txt
  threshold: 0.5

alias_file: tests/fixtures/entity_alias.tsv

perf:
  port: 32299 # 性能测试端口

RESOURCE_CHANGE_LOG_CONFIGS:
  num_threads: 5
  stat_conf:
    host: yftool-ops-statsd
    port: 9125
    prefix: ""
