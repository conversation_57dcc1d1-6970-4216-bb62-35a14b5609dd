params:
  min_score: 0.1
  stop_ne_tags:
    [
      "age",
      "quantifier",
      "weight",
      "length",
      "speed",
      "area",
      "power",
      "degree",
      "currency",
      "volume",
      "acceleration",
      "temperature",
      "per",
      "loc",
      "waterarea",
      "island",
      "time",
      "warzone",
      "war",
      "job",
    ]
  weapon_types: [
      "aircraft",
      "artillery",
      "explosive",
      "guns",
      "missile",
      "radar",
      "spaceship",
      "tank",
      "warship",
    ] # 武器装备类型
es_params:
  ip: "*************"
  port: 9200
  entity_index: "linking_test"
nlp_client: "http://*************:21021"
similarity_params:
  abbreviation_file: ./data/vocab/abbreviation.csv
  text_schema: ./data/vocab/entity_schema.csv
haystack:
  embedding_dim: 768
  embedding_model: "shibing624/text2vec-base-chinese"
default_name: c
entity_threshold: 0.8
property_threshold: 0.8
relation_threshold: 0.8
NEO4J_CONFIGS:
  db:
    schema: bolt
    host: !env ${GRAPH_HOST|yftool-db-neo4j}
    port: !env ${GRAPH_PORT|7687}
    username: !env ${GRAPH_USERNAME|neo4j}
    password: !env ${GRAPH_PASSWORD|yunfu2017}
    search_limit: 1000
    num_threads: 10
    stat_conf:
      create_node_step: graph_create_nodes
      create_edge_step: graph_create_edges
ES_CONFIGS:
  host: !env ${ES_HOST|http://yftool-db-elasticsearch:9200}
  base: ""
  num_threads: 10
  stat_conf:
    host: yftool-ops-statsd
    port: 9125
    prefix: ""
RESOURCE_CHANGE_LOG_CONFIGS:
  num_threads: 5
  stat_conf:
    host: yftool-ops-statsd
    port: 9125
    prefix: ""
ANALYSIS_CONF:
  analysis:
    analyzer:
      ik_searcher:
        type: custom
        tokenizer: ik_smart
        filter:
          - synonym_filter
          - stopwords_filter
    filter:
      synonym_filter:
        type: synonym
        synonyms_path: analysis/synonym.txt
        updateable: True
      stopwords_filter:
        type: stop
        stopwords_path: analysis/stopwords.txt
        updateable: True
stat_conf:
  host: yftool-ops-statsd
  port: 9125
  prefix: ""
NODES_LIMIT: 50
NODES_SKIP: 0
NAME_WEIGHT: 7
ALIAS_WEIGHT: 3
